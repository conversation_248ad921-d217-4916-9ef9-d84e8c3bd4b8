{"name": "spatie/laravel-package-tools", "description": "Tools for creating Laravel packages", "keywords": ["spatie", "laravel-package-tools"], "homepage": "https://github.com/spatie/laravel-package-tools", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.0", "illuminate/contracts": "^9.28|^10.0|^11.0"}, "require-dev": {"mockery/mockery": "^1.5", "orchestra/testbench": "^7.7|^8.0", "pestphp/pest": "^1.22", "phpunit/phpunit": "^9.5.24", "spatie/pest-plugin-test-time": "^1.1"}, "autoload": {"psr-4": {"Spatie\\LaravelPackageTools\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\LaravelPackageTools\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/pest", "test-coverage": "vendor/bin/pest --coverage"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}