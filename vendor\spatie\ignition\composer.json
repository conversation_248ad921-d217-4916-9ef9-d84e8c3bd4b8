{"name": "spatie/ignition", "description": "A beautiful error page for PHP applications.", "keywords": ["error", "page", "laravel", "flare"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "homepage": "https://flareapp.io/ignition", "license": "MIT", "require": {"php": "^8.0", "ext-json": "*", "ext-mbstring": "*", "spatie/flare-client-php": "^1.7", "symfony/console": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0", "spatie/error-solutions": "^1.0"}, "require-dev": {"illuminate/cache": "^9.52|^10.0|^11.0", "mockery/mockery": "^1.4", "pestphp/pest": "^1.20|^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "psr/simple-cache-implementation": "*", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "vlucas/phpdotenv": "^5.5"}, "suggest": {"openai-php/client": "Require get solutions from OpenAI", "simple-cache-implementation": "To cache solutions from OpenAI"}, "config": {"sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true, "pestphp/pest-plugin": true, "php-http/discovery": false}}, "autoload": {"psr-4": {"Spatie\\Ignition\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\Ignition\\Tests\\": "tests"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"analyse": "vendor/bin/phpstan analyse", "baseline": "vendor/bin/phpstan analyse --generate-baseline", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes", "test": "vendor/bin/pest", "test-coverage": "vendor/bin/phpunit --coverage-html coverage"}, "support": {"issues": "https://github.com/spatie/ignition/issues", "forum": "https://twitter.com/flareappio", "source": "https://github.com/spatie/ignition", "docs": "https://flareapp.io/docs/ignition-for-laravel/introduction"}, "extra": {"branch-alias": {"dev-main": "1.5.x-dev"}}}