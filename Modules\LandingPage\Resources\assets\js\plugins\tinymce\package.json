{"_args": [["tinymce@5.10.7", "C:\\xampp\\htdocs\\Projects\\concept-1-main"]], "_from": "tinymce@5.10.7", "_id": "tinymce@5.10.7", "_inBundle": false, "_integrity": "sha512-9UUjaO0R7FxcFo0oxnd1lMs7H+D0Eh+dDVo5hKbVe1a+VB0nit97vOqlinj+YwgoBDt6/DSCUoWqAYlLI8BLYA==", "_location": "/tinymce", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "tinymce@5.10.7", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "5.10.7", "saveSpec": null, "fetchSpec": "5.10.7"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/tinymce/-/tinymce-5.10.7.tgz", "_spec": "5.10.7", "_where": "C:\\xampp\\htdocs\\Projects\\concept-1-main", "author": {"name": "Tiny Technologies, Inc"}, "bugs": {"url": "https://github.com/tinymce/tinymce/issues"}, "description": "Web based JavaScript HTML WYSIWYG editor control.", "homepage": "https://www.tiny.cloud/", "keywords": ["wysiwyg", "<PERSON><PERSON><PERSON>", "richtext", "javascript", "html", "text", "rich editor", "rich text editor", "rte", "rich text", "contenteditable", "editing"], "license": "LGPL-2.1", "main": "tinymce.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/tinymce/tinymce.git", "directory": "modules/tinymce"}, "types": "tinymce.d.ts", "version": "5.10.7"}