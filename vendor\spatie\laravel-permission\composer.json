{"name": "spatie/laravel-permission", "description": "Permission handling for Laravel 8.0 and up", "license": "MIT", "keywords": ["spatie", "laravel", "permission", "permissions", "roles", "acl", "rbac", "security"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "homepage": "https://github.com/spatie/laravel-permission", "require": {"php": "^8.0", "illuminate/auth": "^8.12|^9.0|^10.0|^11.0", "illuminate/container": "^8.12|^9.0|^10.0|^11.0", "illuminate/contracts": "^8.12|^9.0|^10.0|^11.0", "illuminate/database": "^8.12|^9.0|^10.0|^11.0"}, "require-dev": {"laravel/passport": "^11.0|^12.0", "orchestra/testbench": "^6.23|^7.0|^8.0|^9.0", "phpunit/phpunit": "^9.4|^10.1"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Spatie\\Permission\\": "src"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Spatie\\Permission\\Tests\\": "tests"}}, "config": {"sort-packages": true}, "extra": {"branch-alias": {"dev-main": "6.x-dev", "dev-master": "6.x-dev"}, "laravel": {"providers": ["Spatie\\Permission\\PermissionServiceProvider"]}}, "scripts": {"test": "phpunit", "format": "php-cs-fixer fix --allow-risky=yes", "analyse": "phpstan analyse"}}