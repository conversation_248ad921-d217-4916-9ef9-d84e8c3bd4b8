{"_from": "tinymce@5.10.3", "_id": "tinymce@5.10.3", "_inBundle": false, "_integrity": "sha512-O59ssHNnujWvSk5Gt8hIGrdNCMKVWVQv9F8siAgLTRgTh0t3NDHrP1UlLtCxArUi9DPWZvlBeUz8D5fJTu7vnA==", "_location": "/tinymce", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "tinymce@5.10.3", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "5.10.3", "saveSpec": null, "fetchSpec": "5.10.3"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/tinymce/-/tinymce-5.10.3.tgz", "_shasum": "9485cf00159fd70c4948cb5e9dd49bce2a775899", "_spec": "tinymce@5.10.3", "_where": "D:\\git-repos\\client-ri-2\\client-rajodia", "author": {"name": "Tiny Technologies, Inc"}, "bugs": {"url": "https://github.com/tinymce/tinymce/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Web based JavaScript HTML WYSIWYG editor control.", "homepage": "https://www.tiny.cloud/", "keywords": ["wysiwyg", "<PERSON><PERSON><PERSON>", "richtext", "javascript", "html", "text", "rich editor", "rich text editor", "rte", "rich text", "contenteditable", "editing"], "license": "LGPL-2.1", "main": "tinymce.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/tinymce/tinymce.git", "directory": "modules/tinymce"}, "types": "tinymce.d.ts", "version": "5.10.3"}