{"name": "spatie/laravel-google-calendar", "description": "Manage events on a Google Calendar", "keywords": ["spatie", "calendar", "google", "event", "schedule", "laravel-google-calendar"], "homepage": "https://github.com/spatie/laravel-google-calendar", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "require": {"php": "^7.2|^8.0", "google/apiclient": "^2.2", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "nesbot/carbon": "^2.63|^3.0"}, "require-dev": {"mockery/mockery": "^1.3.3|^1.4", "orchestra/testbench": "^4.0|^5.0|^6.0|^7.0|^8.0|^9.0", "phpunit/phpunit": "^8.4|^9.0|^10.5"}, "autoload": {"psr-4": {"Spatie\\GoogleCalendar\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\GoogleCalendar\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/phpunit"}, "extra": {"laravel": {"providers": ["Spatie\\GoogleCalendar\\GoogleCalendarServiceProvider"], "aliases": {"GoogleCalendar": "Spatie\\GoogleCalendar\\GoogleCalendarFacade"}}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}